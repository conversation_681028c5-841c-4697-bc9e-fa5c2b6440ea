pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/* Tomorrow Night Blue Theme */
/* http://jmblog.github.com/color-themes-for-google-code-highlightjs */
/* Original theme - https://github.com/chriskempson/tomorrow-theme */
/* http://jmblog.github.com/color-themes-for-google-code-highlightjs */
/* Tomorrow Comment */
.hljs-comment,
.hljs-quote {
  color: #7285b7
}
/* Tomorrow Red */
.hljs-variable,
.hljs-template-variable,
.hljs-tag,
.hljs-name,
.hljs-selector-id,
.hljs-selector-class,
.hljs-regexp,
.hljs-deletion {
  color: #ff9da4
}
/* Tomorrow Orange */
.hljs-number,
.hljs-built_in,
.hljs-literal,
.hljs-type,
.hljs-params,
.hljs-meta,
.hljs-link {
  color: #ffc58f
}
/* Tomorrow Yellow */
.hljs-attribute {
  color: #ffeead
}
/* Tomorrow Green */
.hljs-string,
.hljs-symbol,
.hljs-bullet,
.hljs-addition {
  color: #d1f1a9
}
/* Tomorrow Blue */
.hljs-title,
.hljs-section {
  color: #bbdaff
}
/* Tomorrow Purple */
.hljs-keyword,
.hljs-selector-tag {
  color: #ebbbff
}
.hljs {
  background: #002451;
  color: white
}
.hljs-emphasis {
  font-style: italic
}
.hljs-strong {
  font-weight: bold
}