pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Solar Flare Light
  Author: <PERSON> (https://chuck.harmston.ch)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme solar-flare-light
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #F5F7FA  Default Background
base01  #E8E9ED  Lighter Background (Used for status bars, line number and folding marks)
base02  #A6AFB8  Selection Background
base03  #85939E  Comments, Invisibles, Line Highlighting
base04  #667581  Dark Foreground (Used for status bars)
base05  #586875  Default Foreground, Caret, Delimiters, Operators
base06  #222E38  Light Foreground (Not often used)
base07  #18262F  Light Background (Not often used)
base08  #EF5253  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #E66B2B  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #E4B51C  Classes, Markup Bold, Search Text Background
base0B  #7CC844  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #52CBB0  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #33B5E1  Functions, Methods, Attribute IDs, Headings
base0E  #A363D5  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #D73C9A  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #586875;
  background: #F5F7FA
}
.hljs::selection,
.hljs ::selection {
  background-color: #A6AFB8;
  color: #586875
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #85939E -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #85939E
}
/* base04 - #667581 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #667581
}
/* base05 - #586875 -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #586875
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #EF5253
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #E66B2B
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #E4B51C
}
.hljs-strong {
  font-weight: bold;
  color: #E4B51C
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #7CC844
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #52CBB0
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #33B5E1
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #A363D5
}
.hljs-emphasis {
  color: #A363D5;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #D73C9A
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}