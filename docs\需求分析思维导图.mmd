graph TB
classDef package-node fill:#a29bfe,stroke:#2d3436,shape:ellipse
classDef package-scope-node fill:#ffeaa7,stroke:#2d3436,shape:stadium

  %% Package Scopes

  %% Node Definitions & Styles
  node0["ABHS"];
  style node0 fill:#74b9ff,stroke:#333,stroke-width:1px
  node1["idea"];
  style node1 fill:#74b9ff,stroke:#333,stroke-width:1px
  node2["______doc"];
  style node2 fill:#74b9ff,stroke:#333,stroke-width:1px
  node3["inspectionProfiles"];
  style node3 fill:#74b9ff,stroke:#333,stroke-width:1px
  node4["00-___.md"];
  style node4 fill:#81ecec,stroke:#333,stroke-width:1px
  node5["01-____"];
  style node5 fill:#74b9ff,stroke:#333,stroke-width:1px
  node6["02-____"];
  style node6 fill:#74b9ff,stroke:#333,stroke-width:1px
  node7["03-____"];
  style node7 fill:#74b9ff,stroke:#333,stroke-width:1px
  node8["01-_______.md"];
  style node8 fill:#81ecec,stroke:#333,stroke-width:1px
  node9["02-______.md"];
  style node9 fill:#81ecec,stroke:#333,stroke-width:1px
  node10["03-_______.md"];
  style node10 fill:#81ecec,stroke:#333,stroke-width:1px
  node11["04-______.md"];
  style node11 fill:#81ecec,stroke:#333,stroke-width:1px
  node12["05-__________.md"];
  style node12 fill:#81ecec,stroke:#333,stroke-width:1px
  node13["06-______.md"];
  style node13 fill:#81ecec,stroke:#333,stroke-width:1px
  node14["README.md"];
  style node14 fill:#81ecec,stroke:#333,stroke-width:1px
  node15["01-________.md"];
  style node15 fill:#81ecec,stroke:#333,stroke-width:1px
  node16["02-__________.md"];
  style node16 fill:#81ecec,stroke:#333,stroke-width:1px
  node17["03-__________.md"];
  style node17 fill:#81ecec,stroke:#333,stroke-width:1px
  node18["04-__________.md"];
  style node18 fill:#81ecec,stroke:#333,stroke-width:1px
  node19["05-________.md"];
  style node19 fill:#81ecec,stroke:#333,stroke-width:1px
  node20["06-_________.md"];
  style node20 fill:#81ecec,stroke:#333,stroke-width:1px
  node21["07-_________.md"];
  style node21 fill:#81ecec,stroke:#333,stroke-width:1px
  node22["08-API____.md"];
  style node22 fill:#81ecec,stroke:#333,stroke-width:1px
  node23["09-______.md"];
  style node23 fill:#81ecec,stroke:#333,stroke-width:1px
  node24["README.md"];
  style node24 fill:#81ecec,stroke:#333,stroke-width:1px
  node25["01-__________.md"];
  style node25 fill:#81ecec,stroke:#333,stroke-width:1px
  node26["02-__________.md"];
  style node26 fill:#81ecec,stroke:#333,stroke-width:1px

  %% Edge Definitions
  node0 --> node1
  linkStyle 0 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node0 --> node2
  linkStyle 1 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node1 --> node3
  linkStyle 2 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node2 --> node4
  linkStyle 3 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node2 --> node5
  linkStyle 4 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node2 --> node6
  linkStyle 5 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node2 --> node7
  linkStyle 6 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node5 --> node8
  linkStyle 7 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node5 --> node9
  linkStyle 8 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node5 --> node10
  linkStyle 9 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node5 --> node11
  linkStyle 10 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node5 --> node12
  linkStyle 11 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node5 --> node13
  linkStyle 12 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node5 --> node14
  linkStyle 13 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node6 --> node15
  linkStyle 14 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node6 --> node16
  linkStyle 15 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node6 --> node17
  linkStyle 16 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node6 --> node18
  linkStyle 17 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node6 --> node19
  linkStyle 18 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node6 --> node20
  linkStyle 19 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node6 --> node21
  linkStyle 20 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node6 --> node22
  linkStyle 21 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node6 --> node23
  linkStyle 22 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node6 --> node24
  linkStyle 23 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node7 --> node25
  linkStyle 24 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node7 --> node26
  linkStyle 25 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5