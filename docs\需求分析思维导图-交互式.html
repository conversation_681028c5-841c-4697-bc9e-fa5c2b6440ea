<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Hybrid View - 需求分析思维导图-交互式.html</title>
  <!-- Load Mermaid from CDN -->
  <script src="https://cdn.jsdelivr.net/npm/mermaid@10.9.1/dist/mermaid.min.js"></script>
  <style>
    body {
      font-family: 'Inter', sans-serif;
      margin: 0;
      padding: 0;
      min-height: 100vh;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      transition: background 0.5s ease;
    }
    .dark-mode {
      background: linear-gradient(135deg, #1e1e2f 0%, #1d2426 100%);
    }
    .light-mode {
      background: linear-gradient(135deg, #f5f6fa 0%, #dcdde1 100%);
    }
    header {
      position: absolute;
      top: 20px;
      left: 20px;
      text-align: left;
    }
    #theme-toggle {
      position: absolute;
      top: 20px;
      right: 20px;
      padding: 10px 20px;
      border: none;
      border-radius: 50px;
      font-size: 14px;
      cursor: pointer;
      transition: all 0.3s ease;
    }
    #diagram-container {
      width: 90%;
      max-width: 1200px;
      margin: 75px 0;
      padding: 25px;
      border-radius: 15px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
      transition: all 0.5s ease;
      position: relative;
    }
    #mermaid-graph {
      overflow: auto;
      max-height: 70vh;
    }
    #error-message {
      position: absolute;
      bottom: 10px;
      left: 10px;
      padding: 8px 16px;
      border-radius: 8px;
      font-size: 14px;
      display: none;
    }
    /* Styles for collapsed nodes */
    .collapsed-node text {
      font-weight: bold;
    }
    .collapsed-node rect, .collapsed-node circle, .collapsed-node polygon {
      stroke-width: 3px !important;
    }
    .collapsed-indicator {
      fill: #4cd137;
      font-weight: bold;
    }
    /* Add + symbol to collapsed nodes */
    .collapsed-node .collapsed-icon {
      fill: #4cd137;
      font-size: 16px;
      font-weight: bold;
    }
  </style>
</head>
<body class="light-mode">
  <!-- Header -->
  <header style="color: #2d3436;">
    <h1 style="margin: 0; font-size: 28px;">Hybrid View - 需求分析思维导图-交互式.html</h1>
    <div style="font-size: 14px; margin-top: 5px;">Generated on Tue Aug 05 2025 13:49:29</div>
  </header>

  <!-- Theme Toggle Button - Initial state for light mode -->
  <button id="theme-toggle" style="background: #dcdde1; color: #2d3436;">Switch to Dark Mode</button>

  <!-- Diagram Container - Initial state for light mode -->
  <div id="diagram-container" style="background: rgba(255, 255, 255, 0.8); border: 1px solid rgba(0, 0, 0, 0.1);">
    <div id="mermaid-graph"></div>
    <div id="error-message" style="background: rgba(45, 52, 54, 0.9); color: #ff7675;"></div>
    <!-- Mermaid Code -->
    <pre id="raw-code" style="display: none;">
graph TB
classDef package-node fill:#a29bfe,stroke:#2d3436,shape:ellipse
classDef package-scope-node fill:#ffeaa7,stroke:#2d3436,shape:stadium

  %% Package Scopes

  %% Node Definitions & Styles
  node0["ABHS"];
  style node0 fill:#74b9ff,stroke:#333,stroke-width:1px
  node1["idea"];
  style node1 fill:#74b9ff,stroke:#333,stroke-width:1px
  node2["______doc"];
  style node2 fill:#74b9ff,stroke:#333,stroke-width:1px
  node3["ABHS.iml"];
  style node3 fill:#81ecec,stroke:#333,stroke-width:1px
  node4["AugmentWebviewStateStore.xml"];
  style node4 fill:#81ecec,stroke:#333,stroke-width:1px
  node5["inspectionProfiles"];
  style node5 fill:#74b9ff,stroke:#333,stroke-width:1px
  node6["misc.xml"];
  style node6 fill:#81ecec,stroke:#333,stroke-width:1px
  node7["modules.xml"];
  style node7 fill:#81ecec,stroke:#333,stroke-width:1px
  node8["workspace.xml"];
  style node8 fill:#81ecec,stroke:#333,stroke-width:1px
  node9["profiles_settings.xml"];
  style node9 fill:#81ecec,stroke:#333,stroke-width:1px
  node10["Project_Default.xml"];
  style node10 fill:#81ecec,stroke:#333,stroke-width:1px
  node11["00-___.md"];
  style node11 fill:#81ecec,stroke:#333,stroke-width:1px
  node12["01-____"];
  style node12 fill:#74b9ff,stroke:#333,stroke-width:1px
  node13["02-____"];
  style node13 fill:#74b9ff,stroke:#333,stroke-width:1px
  node14["03-____"];
  style node14 fill:#74b9ff,stroke:#333,stroke-width:1px
  node15["01-_______.md"];
  style node15 fill:#81ecec,stroke:#333,stroke-width:1px
  node16["02-______.md"];
  style node16 fill:#81ecec,stroke:#333,stroke-width:1px
  node17["03-_______.md"];
  style node17 fill:#81ecec,stroke:#333,stroke-width:1px
  node18["04-______.md"];
  style node18 fill:#81ecec,stroke:#333,stroke-width:1px
  node19["05-__________.md"];
  style node19 fill:#81ecec,stroke:#333,stroke-width:1px
  node20["06-______.md"];
  style node20 fill:#81ecec,stroke:#333,stroke-width:1px
  node21["README.md"];
  style node21 fill:#81ecec,stroke:#333,stroke-width:1px
  node22["01-________.md"];
  style node22 fill:#81ecec,stroke:#333,stroke-width:1px
  node23["02-__________.md"];
  style node23 fill:#81ecec,stroke:#333,stroke-width:1px
  node24["03-__________.md"];
  style node24 fill:#81ecec,stroke:#333,stroke-width:1px
  node25["04-__________.md"];
  style node25 fill:#81ecec,stroke:#333,stroke-width:1px
  node26["05-________.md"];
  style node26 fill:#81ecec,stroke:#333,stroke-width:1px
  node27["06-_________.md"];
  style node27 fill:#81ecec,stroke:#333,stroke-width:1px
  node28["07-_________.md"];
  style node28 fill:#81ecec,stroke:#333,stroke-width:1px
  node29["08-API____.md"];
  style node29 fill:#81ecec,stroke:#333,stroke-width:1px
  node30["09-______.md"];
  style node30 fill:#81ecec,stroke:#333,stroke-width:1px
  node31["README.md"];
  style node31 fill:#81ecec,stroke:#333,stroke-width:1px
  node32["01-__________.md"];
  style node32 fill:#81ecec,stroke:#333,stroke-width:1px
  node33["02-__________.md"];
  style node33 fill:#81ecec,stroke:#333,stroke-width:1px

  %% Edge Definitions
  node0 --> node1
  linkStyle 0 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node0 --> node2
  linkStyle 1 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node1 --> node3
  linkStyle 2 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node1 --> node4
  linkStyle 3 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node1 --> node5
  linkStyle 4 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node1 --> node6
  linkStyle 5 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node1 --> node7
  linkStyle 6 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node1 --> node8
  linkStyle 7 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node5 --> node9
  linkStyle 8 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node5 --> node10
  linkStyle 9 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node2 --> node11
  linkStyle 10 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node2 --> node12
  linkStyle 11 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node2 --> node13
  linkStyle 12 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node2 --> node14
  linkStyle 13 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node12 --> node15
  linkStyle 14 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node12 --> node16
  linkStyle 15 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node12 --> node17
  linkStyle 16 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node12 --> node18
  linkStyle 17 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node12 --> node19
  linkStyle 18 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node12 --> node20
  linkStyle 19 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node12 --> node21
  linkStyle 20 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node13 --> node22
  linkStyle 21 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node13 --> node23
  linkStyle 22 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node13 --> node24
  linkStyle 23 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node13 --> node25
  linkStyle 24 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node13 --> node26
  linkStyle 25 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node13 --> node27
  linkStyle 26 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node13 --> node28
  linkStyle 27 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node13 --> node29
  linkStyle 28 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node13 --> node30
  linkStyle 29 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node13 --> node31
  linkStyle 30 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node14 --> node32
  linkStyle 31 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node14 --> node33
  linkStyle 32 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
    </pre>
  </div>

  <script>
    // Unique render ID counter
    let renderCount = 0;

    // Track collapsible groups
    const collapsibleGroups = {};
    let expandedGroups = new Set();
    let collapsedGroups = new Set();

    // Initialize Mermaid with light theme by default
    mermaid.initialize({
      startOnLoad: false,
      theme: 'default',
      securityLevel: 'loose',
      flowchart: {
        htmlLabels: true,
        curve: 'basis',
        nodeSpacing: 42,
        rankSpacing: 60,
        useMaxWidth: true
      },
      themeVariables: {
        // Default light theme variables (adjust if needed)
        nodeBorder: "#2d3436",
        mainBkg: "#f8f9fa",    // Light background
        nodeTextColor: "#333333", // Dark text
        fontSize: "16px"
      }
    });

    // Render on DOM load
    document.addEventListener('DOMContentLoaded', () => {
      if (typeof mermaid === 'undefined') {
        log('Mermaid library failed to load. Check network or CDN URL.');
        document.getElementById('error-message').style.display = 'block';
        document.getElementById('error-message').textContent = 'Error: Mermaid library not loaded';
        return;
      }
      renderMermaid();
    });

    // Handle node click events
    window.toggleGroup = function(nodeId) {
      if (expandedGroups.has(nodeId)) {
        // Collapse the group
        expandedGroups.delete(nodeId);
        collapsedGroups.add(nodeId);
      } else {
        // Expand the group
        collapsedGroups.delete(nodeId);
        expandedGroups.add(nodeId);
      }
      renderMermaid();
    };

    // Re-add processMermaidSvg function
    function processMermaidSvg(svgElement) {
      // Process click events on nodes
      const clickables = svgElement.querySelectorAll('[id^="flowchart-"]');
      
      clickables.forEach(node => {
        const nodeId = node.id.replace('flowchart-', '');
        
        // Is this a collapsible group?
        if (Object.keys(collapsibleGroups).includes(nodeId)) {
          // Add visual indicator for collapsed/expanded state
          const textElement = node.querySelector('text');
          
          if (textElement && collapsedGroups.has(nodeId)) {
            // Add a + sign for collapsed groups
            const currentText = textElement.textContent || '';
            if (!currentText.includes('[+]')) {
              textElement.textContent = currentText + ' [+]';
            }
            
            // Add a class for styling
            node.classList.add('collapsed-node');
          }
          
          // Make nodes clickable visually
          node.style.cursor = 'pointer';
          
          // Add the children count to the label
          const childCount = collapsibleGroups[nodeId].length;
          const childLabel = '(' + childCount + ' items)';
          const label = node.querySelector('text');
          
          if (label && !label.textContent.includes(childLabel)) {
            label.textContent += ' ' + childLabel;
          }
        }
      });
      
      // Hide children of collapsed groups
      collapsedGroups.forEach(groupId => {
        const children = collapsibleGroups[groupId] || [];
        children.forEach(childId => {
          const childElement = svgElement.querySelector('#flowchart-' + childId);
          if (childElement) {
            childElement.style.display = 'none';
            
            // Also hide edges to/from this element
            const edges = svgElement.querySelectorAll('path.flowchart-link');
            edges.forEach(edge => {
              const edgeId = edge.id;
              if (edgeId.includes(childId)) {
                edge.style.display = 'none';
              }
            });
          }
        });
      });
    }

    // Re-add detectCollapsibleGroups function
    function detectCollapsibleGroups(mermaidCode) {
      // Reset the collapsible groups
      Object.keys(collapsibleGroups).forEach(key => delete collapsibleGroups[key]);

      // Look for click handler definitions like 'click node1 toggleGroup "node1"'
      // Ensure backslashes for regex characters and quotes are properly escaped for the final HTML
      const clickHandlerRegex = /click\s+(\w+)\s+toggleGroup\s+"([^"]+)"/g;
      let match;
      
      while ((match = clickHandlerRegex.exec(mermaidCode)) !== null) {
        const nodeId = match[1];
        
        // Now find children of this group in the subgraph definition
        // Ensure backslashes for regex characters are properly escaped for the final HTML
        const subgraphRegex = new RegExp('subgraph\s+' + nodeId + '.*?\n([\s\S]*?)\nend', 'g');
        const subgraphMatch = subgraphRegex.exec(mermaidCode);
        
        if (subgraphMatch) {
          const subgraphContent = subgraphMatch[1];
          // Extract node IDs from the subgraph
          // Ensure backslashes for regex characters are properly escaped for the final HTML
          const nodeRegex = /\s+(\w+)/g;
          const children = [];
          let nodeMatch;
          
          while ((nodeMatch = nodeRegex.exec(subgraphContent)) !== null) {
            const childId = nodeMatch[1].trim();
            if (childId !== nodeId) {
              children.push(childId);
            }
          }
          
          if (children.length > 0) {
            collapsibleGroups[nodeId] = children;
            // By default, all groups start expanded
            expandedGroups.add(nodeId);
          }
        }
      }
      
      log('Detected collapsible groups: ' + JSON.stringify(collapsibleGroups));
    }

    // Render Mermaid diagram
    function renderMermaid() {
      const mermaidDiv = document.getElementById('mermaid-graph');
      const errorDiv = document.getElementById('error-message');
      const rawCode = document.getElementById('raw-code').textContent.trim();
      const uniqueId = 'mermaid-svg-' + Date.now() + '-' + renderCount++;

      // Detect collapsible groups in the diagram
      detectCollapsibleGroups(rawCode);

      // Clear previous content
      mermaidDiv.innerHTML = '';
      errorDiv.style.display = 'none';

      // Render using promise
      mermaid.render(uniqueId, rawCode)
        .then(({ svg }) => {
          mermaidDiv.innerHTML = svg;
          
          // Process the SVG after it's been inserted into the DOM
          const svgElement = mermaidDiv.querySelector('svg');
          if (svgElement) {
            processMermaidSvg(svgElement);
          }
        })
        .catch(error => {
          log('Mermaid rendering failed: ' + error);
          errorDiv.style.display = 'block';
          errorDiv.textContent = error.message;
          
          // Create a <pre> element and set its text content safely
          const preElement = document.createElement('pre');
          preElement.style.color = '#ff7675'; // Apply style directly
          preElement.textContent = rawCode; // Use textContent for safety
          
          // Clear mermaidDiv and append the new <pre> element
          mermaidDiv.innerHTML = ''; // Clear previous attempts
          mermaidDiv.appendChild(preElement);
        });
    }

    // Theme toggle function
    function toggleTheme() {
      const body = document.body;
      const toggleBtn = document.getElementById('theme-toggle');
      const diagramContainer = document.getElementById('diagram-container');
      const header = document.querySelector('header');
      const isDarkMode = body.classList.contains('dark-mode');

      if (isDarkMode) {
        // Switch to Light Mode
        body.classList.remove('dark-mode');
        body.classList.add('light-mode');
        toggleBtn.textContent = 'Switch to Dark Mode';
        toggleBtn.style.background = '#dcdde1';
        toggleBtn.style.color = '#2d3436';
        diagramContainer.style.background = 'rgba(255, 255, 255, 0.8)';
        diagramContainer.style.border = '1px solid rgba(0, 0, 0, 0.1)';
        header.style.color = '#2d3436';
        
        // Update Mermaid theme to light with dark text
        mermaid.initialize({
          theme: 'default',
          themeVariables: {
            nodeBorder: "#2d3436",
            mainBkg: "#f8f9fa",
            nodeTextColor: "#333333",
            fontSize: "16px"
          }
        });
      } else {
        // Switch to Dark Mode
        body.classList.remove('light-mode');
        body.classList.add('dark-mode');
        toggleBtn.textContent = 'Switch to Light Mode';
        toggleBtn.style.background = '#2d3436';
        toggleBtn.style.color = '#ffffff';
        diagramContainer.style.background = 'rgba(255, 255, 255, 0.05)';
        diagramContainer.style.border = '1px solid rgba(255, 255, 255, 0.1)';
        header.style.color = '#ffffff';
        
        // Update Mermaid theme to dark with bright white text
        mermaid.initialize({
          theme: 'dark',
          themeVariables: {
            nodeBorder: "#2d3436",
            mainBkg: "#1e272e",
            nodeTextColor: "#ffffff",
            fontSize: "16px"
          }
        });
      }

      // Re-render diagram after theme change
      renderMermaid();
    }

    // Attach theme toggle event
    document.getElementById('theme-toggle').addEventListener('click', toggleTheme);
  </script>
</body>
</html>