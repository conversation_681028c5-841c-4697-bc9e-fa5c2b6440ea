# Markmap MCP Server - 本地安装版本

## 📁 目录结构
```
markmap-mcp-server/
├── node_modules/           # 依赖包（隔离安装）
├── output/                 # 输出目录
├── start.bat              # Windows 启动脚本
├── start.sh               # Linux/Mac 启动脚本
├── test.bat               # 测试脚本
├── augment-config.json    # Augment 配置文件
└── README.md              # 本文档
```

## 🚀 启动方式

### Windows
```bash
start.bat
```

### Linux/Mac
```bash
chmod +x start.sh
./start.sh
```

## 🔧 Augment 配置

将 `augment-config.json` 中的配置添加到 Augment 的 MCP 配置中：

```json
{
  "mcpServers": {
    "markmap": {
      "type": "stdio",
      "command": "node",
      "args": [
        "f:\\MCP\\markmap-mcp-server\\node_modules\\@jinzcdev\\markmap-mcp-server\\build\\index.js"
      ],
      "env": {
        "MARKMAP_DIR": "f:\\MCP\\markmap-mcp-server\\output"
      }
    }
  }
}
```

## 📋 功能特性

- ✅ Markdown 转思维导图
- ✅ 支持 PNG/JPG/SVG 导出
- ✅ 交互式操作（缩放、展开/折叠）
- ✅ 自动浏览器预览
- ✅ 依赖隔离安装

## 🧪 测试

运行测试脚本：
```bash
test.bat
```

## 📝 使用说明

1. 启动服务器：`start.bat`
2. 在 Augment 中使用 `markdown-to-mindmap` 工具
3. 生成的文件保存在 `output/` 目录中
