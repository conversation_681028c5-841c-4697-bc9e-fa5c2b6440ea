import { ServerType } from '@hono/node-server';
import { Transformer } from 'markmap-lib';
import { AddressInfo } from 'net';
import { IContentProvider, IDevelopOptions } from '../types';
export declare class MarkmapDevServer {
    options: IDevelopOptions;
    providers: Record<string, IContentProvider>;
    private transformer;
    private html;
    private watcher;
    private callbacks;
    private disposeList;
    serverInfo: {
        server: ServerType;
        address: AddressInfo;
    } | null;
    constructor(options: IDevelopOptions, transformer?: Transformer);
    private _buildHtml;
    setup(): Promise<void>;
    shutdown(): Promise<void>;
    destroy(): Promise<void>;
    private _watch;
    addProvider(options?: {
        key?: string;
        filePath?: string;
    }): IContentProvider;
    delProvider(key: string): void;
}
export declare function develop(options: IDevelopOptions): Promise<MarkmapDevServer>;
