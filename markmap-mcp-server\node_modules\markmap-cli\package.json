{"name": "markmap-cli", "version": "0.18.12", "description": "Create markmaps from CLI", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bin": {"markmap": "bin/cli.js"}, "engines": {"node": ">=18"}, "keywords": ["markmap", "markdown", "mindmap"], "homepage": "https://github.com/markmap/markmap/packages/markmap-cli#readme", "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org/"}, "repository": {"type": "git", "url": "git+https://github.com/markmap/markmap.git"}, "scripts": {"clean": "del-cli dist tsconfig.tsbuildinfo", "build:types": "tsc", "build:js": "vite build && pnpm fetch-assets", "build": "pnpm clean && pnpm /^build:/", "fetch-assets": "node ./dist/fetch-assets.js", "prepublishOnly": "pnpm build"}, "bugs": {"url": "https://github.com/markmap/markmap/issues"}, "type": "module", "main": "dist/index.js", "module": "dist/index.js", "files": ["bin", "dist"], "typings": "dist/index.d.ts", "dependencies": {"@babel/runtime": "^7.26.0", "@hono/node-server": "^1.13.7", "chokidar": "^4.0.1", "commander": "^12.1.0", "hono": "^4.6.13", "markmap-common": "0.18.9", "markmap-lib": "0.18.12", "markmap-render": "0.18.12", "markmap-toolbar": "0.18.12", "open": "^10.1.0", "portfinder": "^1.0.32", "read-package-up": "^11.0.0", "update-notifier": "^7.3.1"}, "gitHead": "205367a24603dc187f67da1658940c6cade20dce"}